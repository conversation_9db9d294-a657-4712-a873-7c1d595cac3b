/**
 * Create a specific scan for testing the scan details page
 */

const { Client } = require('pg');

async function createSpecificScan() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'complychecker_dev',
    user: 'complyuser',
    password: 'complypassword',
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    const scanId = '69d37955-7a8a-4e45-878e-dc72129b0e42';
    const userId = '9fed30a7-64b4-4ffe-b531-6e9cf592952b';

    // Check if scan already exists
    const existingQuery = 'SELECT id FROM wcag_scans WHERE id = $1';
    const existing = await client.query(existingQuery, [scanId]);

    if (existing.rows.length > 0) {
      console.log('✅ Scan already exists');
      return;
    }

    console.log('🔄 Creating specific scan...');

    // Insert the specific scan
    await client.query(`
      INSERT INTO wcag_scans (
        id, user_id, target_url, scan_status, scan_timestamp, completion_timestamp,
        overall_score, level_achieved, risk_level, perceivable_score, operable_score,
        understandable_score, robust_score, wcag21_score, wcag22_score, wcag30_score,
        total_automated_checks, passed_automated_checks, failed_automated_checks,
        manual_review_items, scan_options
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
      )
    `, [
      scanId,
      userId,
      'https://example.com/accessibility-test',
      'completed',
      new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
      new Date(Date.now() - 3 * 60 * 60 * 1000 + 8 * 60 * 1000), // 8 minutes later
      82.7,
      'AA',
      'low',
      88,
      79,
      81,
      83,
      82,
      83,
      82,
      32,
      26,
      6,
      3,
      JSON.stringify({
        maxPages: 8,
        enableContrastAnalysis: true,
        enableKeyboardTesting: true,
        enableFocusAnalysis: true,
        enableSemanticValidation: true,
        wcagVersion: 'all',
        level: 'AA'
      })
    ]);

    console.log('✅ Created specific scan');

    // Create automated results
    await client.query(`
      INSERT INTO wcag_automated_results (
        id, scan_id, rule_id, rule_name, category, wcag_version, success_criterion,
        conformance_level, status, score, max_score, weight, evidence, recommendations, execution_time
      ) VALUES 
      ($1, $2, 'WCAG-001', 'Image Alternative Text', 'perceivable', '2.1', '1.1.1', 'A', 'passed', 100, 100, 1.0, $3, $4, 150),
      ($5, $2, 'WCAG-007', 'Focus Visible', 'operable', '2.1', '2.4.7', 'AA', 'failed', 65, 100, 1.0, $6, $7, 200),
      ($8, $2, 'WCAG-015', 'Color Contrast', 'perceivable', '2.1', '1.4.3', 'AA', 'passed', 95, 100, 1.0, $9, $10, 180),
      ($11, $2, 'WCAG-023', 'Keyboard Navigation', 'operable', '2.1', '2.1.1', 'A', 'warning', 80, 100, 1.0, $12, $13, 220),
      ($14, $2, 'WCAG-031', 'Page Titles', 'operable', '2.1', '2.4.2', 'A', 'passed', 100, 100, 1.0, $15, $16, 120)
    `, [
      'result-specific-001',
      scanId,
      JSON.stringify([{"type": "text", "description": "All images have appropriate alt text", "value": "18 images checked", "severity": "info"}]),
      JSON.stringify(["Continue providing descriptive alt text for all images"]),
      'result-specific-002',
      JSON.stringify([{"type": "text", "description": "Some elements lack visible focus indicators", "value": "5 elements failed", "severity": "error"}]),
      JSON.stringify(["Add visible focus indicators to all interactive elements", "Ensure focus indicators have sufficient contrast"]),
      'result-specific-003',
      JSON.stringify([{"type": "text", "description": "Good contrast ratios found", "value": "All text meets AA standards", "severity": "info"}]),
      JSON.stringify(["Maintain current contrast levels"]),
      'result-specific-004',
      JSON.stringify([{"type": "text", "description": "Most elements are keyboard accessible", "value": "2 elements need improvement", "severity": "warning"}]),
      JSON.stringify(["Ensure all interactive elements are keyboard accessible", "Add proper focus management"]),
      'result-specific-005',
      JSON.stringify([{"type": "text", "description": "All pages have descriptive titles", "value": "8 pages checked", "severity": "info"}]),
      JSON.stringify(["Continue using descriptive page titles"])
    ]);

    console.log('✅ Created automated results');
    console.log('🎉 Specific scan created successfully!');

  } catch (error) {
    console.error('❌ Error creating specific scan:', error);
  } finally {
    await client.end();
  }
}

createSpecificScan();
