'use client';

/**
 * WCAG History Page
 * Page for viewing scan history and managing past scans
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  Globe,
} from 'lucide-react';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

/**
 * WCAG History Content Component
 */
const WcagHistoryContent: React.FC = () => {
  const router = useRouter();
  const state = useWcagState();
  const actions = useWcagActions();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date');

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Load scans data
  useEffect(() => {
    const loadScans = async () => {
      try {
        await actions.fetchScans({
          page: state.pagination.page,
          limit: state.pagination.limit,
          status: statusFilter !== 'all' ? statusFilter : undefined,
        });
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error loading scans:', error);
      }
    };

    if (mounted) {
      loadScans();
    }
  }, [mounted, state.pagination.page, state.pagination.limit, actions.fetchScans, statusFilter]);

  const handleBack = () => {
    router.push('/dashboard/wcag');
  };

  const handleViewScan = (scanId: string) => {
    router.push(`/dashboard/wcag/scan/${scanId}`);
  };

  const handleDeleteScan = async (scanId: string) => {
    if (confirm('Are you sure you want to delete this scan? This action cannot be undone.')) {
      try {
        await actions.deleteScan(scanId);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error deleting scan:', error);
      }
    }
  };

  const handleExportScan = async (scanId: string, format: 'pdf' | 'json' | 'csv') => {
    try {
      await actions.exportScan(scanId, format);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error exporting scan:', error);
    }
  };

  const handlePageChange = (page: number) => {
    actions.setPagination({ page });
  };

  const handleLimitChange = (limit: string) => {
    actions.setPagination({ limit: parseInt(limit), page: 1 });
  };

  // Filter and sort scans
  const filteredScans = state.scans
    .filter((scan) => {
      const matchesSearch = scan.url.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || scan.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.scanTimestamp).getTime() - new Date(a.scanTimestamp).getTime();
        case 'score':
          return (b.overallScore || 0) - (a.overallScore || 0);
        case 'url':
          return a.url.localeCompare(b.url);
        default:
          return 0;
      }
    });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'running':
        return <Clock className="h-4 w-4 text-orange-500" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default">Completed</Badge>;
      case 'running':
        return <Badge variant="secondary">Running</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return <Badge variant="default">Excellent</Badge>;
    if (score >= 70) return <Badge variant="secondary">Good</Badge>;
    if (score >= 50) return <Badge variant="outline">Fair</Badge>;
    return <Badge variant="destructive">Poor</Badge>;
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Scan History</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            View and manage your WCAG compliance scan history
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by URL..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="running">Running</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="score">Score</SelectItem>
                <SelectItem value="url">URL</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Scans List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Scans ({filteredScans.length})</span>
            <div className="flex items-center gap-2">
              <Select value={state.pagination.limit.toString()} onValueChange={handleLimitChange}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-gray-500">per page</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {state.loading.fetching ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : filteredScans.length === 0 ? (
            <div className="text-center py-8">
              <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm || statusFilter !== 'all'
                  ? 'No scans match your filters.'
                  : 'No scans found. Start your first scan!'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredScans.map((scan) => (
                <div
                  key={scan.scanId}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="flex-shrink-0">{getStatusIcon(scan.status)}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-gray-900 dark:text-white truncate">
                          {scan.url}
                        </p>
                        {getStatusBadge(scan.status)}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(scan.scanTimestamp).toLocaleDateString()}
                        </span>
                        <span>WCAG {scan.wcagVersion}</span>
                        <span>Level {scan.complianceLevel}</span>
                      </div>
                    </div>
                    {scan.status === 'completed' && (
                      <div className="text-right">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {scan.overallScore?.toFixed(1)}%
                        </p>
                        {getScoreBadge(scan.overallScore || 0)}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button variant="outline" size="sm" onClick={() => handleViewScan(scan.scanId)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    {scan.status === 'completed' && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExportScan(scan.scanId, 'pdf')}
                          disabled={state.loading.exporting}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteScan(scan.scanId)}
                      disabled={state.loading.deleting}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {state.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Showing {(state.pagination.page - 1) * state.pagination.limit + 1} to{' '}
                {Math.min(state.pagination.page * state.pagination.limit, state.pagination.total)}{' '}
                of {state.pagination.total} results
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(state.pagination.page - 1)}
                  disabled={state.pagination.page <= 1}
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Page {state.pagination.page} of {state.pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(state.pagination.page + 1)}
                  disabled={state.pagination.page >= state.pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Main WCAG History Page with Provider
 */
export default function WcagHistoryPage() {
  return (
    <WcagProvider>
      <WcagHistoryContent />
    </WcagProvider>
  );
}
